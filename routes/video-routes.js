const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

module.exports = (app, upload) => {
    // Upload multiple video files
    app.post('/api/upload-videos', upload.array('videos', 50), async (req, res) => {
        try {
            console.log('Received video upload request');
            console.log('Files:', req.files?.length || 0);
            
            if (!req.files || req.files.length === 0) {
                return res.status(400).json({ error: 'No video files uploaded' });
            }

            const uploadedFiles = req.files.map(file => ({
                id: uuidv4(),
                originalName: file.originalname,
                filename: file.filename,
                path: file.path,
                size: file.size
            }));

            res.json({ 
                success: true, 
                files: uploadedFiles,
                message: `Uploaded ${uploadedFiles.length} video files`
            });
        } catch (error) {
            console.error('Upload error:', error);
            res.status(500).json({ error: 'Failed to upload videos' });
        }
    });

    // Generate random video combinations
    app.post('/api/generate-videos', async (req, res) => {
        try {
            const { files, targetDuration, quantity } = req.body;
            
            console.log('Generate request:', { 
                filesCount: files?.length, 
                targetDuration, 
                quantity 
            });

            if (!files || files.length === 0) {
                return res.status(400).json({ error: 'No video files provided' });
            }

            if (!targetDuration || !quantity) {
                return res.status(400).json({ error: 'Missing targetDuration or quantity' });
            }

            // Set up Server-Sent Events for real-time progress
            res.writeHead(200, {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control'
            });

            const sendProgress = (data) => {
                res.write(`data: ${JSON.stringify(data)}\n\n`);
            };

            const generatedVideos = [];

            for (let i = 0; i < quantity; i++) {
                try {
                    sendProgress({ 
                        type: 'progress', 
                        videoNumber: i + 1, 
                        message: `Starting video ${i + 1} of ${quantity}...`,
                        progress: 0
                    });

                    const videoResult = await generateSingleVideo(
                        files, 
                        targetDuration, 
                        i + 1,
                        (progress, message) => {
                            sendProgress({
                                type: 'progress',
                                videoNumber: i + 1,
                                progress: progress,
                                message: message
                            });
                        }
                    );

                    generatedVideos.push(videoResult);

                    // Send video ready event
                    sendProgress({
                        type: 'video-ready',
                        videoNumber: i + 1,
                        video: videoResult
                    });

                } catch (error) {
                    console.error(`Error generating video ${i + 1}:`, error);
                    sendProgress({
                        type: 'error',
                        videoNumber: i + 1,
                        error: error.message
                    });
                }
            }

            // Send completion event
            sendProgress({
                type: 'complete',
                videos: generatedVideos,
                message: `Successfully generated ${generatedVideos.length} videos`
            });

            res.end();

        } catch (error) {
            console.error('Generation error:', error);
            res.write(`data: ${JSON.stringify({ 
                type: 'error', 
                error: error.message 
            })}\n\n`);
            res.end();
        }
    });

    // Download generated video
    app.get('/api/download/:filename', async (req, res) => {
        try {
            const filename = req.params.filename;
            const filePath = path.join(__dirname, '../output', filename);
            
            // Check if file exists
            await fs.access(filePath);
            
            res.download(filePath, filename, (err) => {
                if (err) {
                    console.error('Download error:', err);
                    res.status(500).json({ error: 'Failed to download file' });
                }
            });
        } catch (error) {
            console.error('File not found:', error);
            res.status(404).json({ error: 'File not found' });
        }
    });

    // Get video file as blob for File System Access API
    app.get('/api/video-blob/:filename', async (req, res) => {
        try {
            const filename = req.params.filename;
            const filePath = path.join(__dirname, '../output', filename);
            
            // Check if file exists
            await fs.access(filePath);
            
            // Read file and send as blob
            const fileBuffer = await fs.readFile(filePath);
            
            res.setHeader('Content-Type', 'video/mp4');
            res.setHeader('Content-Length', fileBuffer.length);
            res.send(fileBuffer);
            
        } catch (error) {
            console.error('Blob fetch error:', error);
            res.status(404).json({ error: 'File not found' });
        }
    });

    // Clean up old files
    app.delete('/api/cleanup', async (req, res) => {
        try {
            const uploadsDir = path.join(__dirname, '../uploads');
            const outputDir = path.join(__dirname, '../output');
            
            // Clean uploads older than 1 hour
            await cleanOldFiles(uploadsDir, 60 * 60 * 1000);
            
            // Clean output older than 24 hours
            await cleanOldFiles(outputDir, 24 * 60 * 60 * 1000);
            
            res.json({ success: true, message: 'Cleanup completed' });
        } catch (error) {
            console.error('Cleanup error:', error);
            res.status(500).json({ error: 'Cleanup failed' });
        }
    });
};

// Helper function to generate a single video
async function generateSingleVideo(files, targetDuration, videoNumber, onProgress) {
    return new Promise(async (resolve, reject) => {
        try {
            // Select random clips
            const selectedClips = selectRandomClips(files, targetDuration);
            
            onProgress(10, 'Selected clips, starting processing...');
            
            const outputFilename = `LazyRemix_${videoNumber}_${Date.now()}.mp4`;
            const outputPath = path.join(__dirname, '../output', outputFilename);
            
            // Create FFmpeg command for concatenating clips
            let command = ffmpeg();

            // Create temporary clip files first
            const tempClips = [];

            onProgress(20, 'Extracting random clips...');

            // Extract each clip to a temporary file
            for (let i = 0; i < selectedClips.length; i++) {
                const clip = selectedClips[i];
                const tempClipPath = path.join(__dirname, '../output', `temp_clip_${videoNumber}_${i}_${Date.now()}.mp4`);
                tempClips.push(tempClipPath);

                console.log(`Extracting clip ${i + 1}: ${clip.startTime}s for ${clip.duration}s from ${clip.file.originalName}`);

                // Extract clip using FFmpeg
                await new Promise((resolve, reject) => {
                    const command = ffmpeg(clip.file.path)
                        .seekInput(clip.startTime)
                        .duration(clip.duration)
                        .outputOptions([
                            '-c:v libx264',
                            '-c:a aac',
                            '-preset ultrafast',
                            '-crf 23'
                        ])
                        .output(tempClipPath)
                        .on('start', (commandLine) => {
                            console.log(`FFmpeg clip extraction command: ${commandLine}`);
                        })
                        .on('stderr', (stderrLine) => {
                            console.log(`FFmpeg stderr: ${stderrLine}`);
                        })
                        .on('end', () => {
                            console.log(`Successfully extracted clip ${i + 1} to ${tempClipPath}`);
                            resolve();
                        })
                        .on('error', (error, stdout, stderr) => {
                            console.error(`FFmpeg clip extraction error for clip ${i + 1}:`, error);
                            console.error('FFmpeg stdout:', stdout);
                            console.error('FFmpeg stderr:', stderr);
                            reject(error);
                        });

                    command.run();
                });
            }

            onProgress(50, 'Combining clips...');

            // Now concatenate all the clips using a simpler approach
            command = ffmpeg();

            // Validate that all temp clips exist and have content
            for (let i = 0; i < tempClips.length; i++) {
                const clipPath = tempClips[i];
                try {
                    const stats = await fs.stat(clipPath);
                    if (stats.size === 0) {
                        throw new Error(`Temp clip ${i + 1} is empty: ${clipPath}`);
                    }
                    console.log(`Validated clip ${i + 1}: ${clipPath} (${stats.size} bytes)`);
                } catch (error) {
                    throw new Error(`Temp clip ${i + 1} validation failed: ${error.message}`);
                }
            }

            // Create a concat demuxer file list with absolute paths
            const concatListPath = path.join(__dirname, '../output', `concat_list_${videoNumber}_${Date.now()}.txt`);
            const concatContent = tempClips.map(clipPath => `file '${clipPath}'`).join('\n');
            await fs.writeFile(concatListPath, concatContent);

            console.log(`Created concat list at: ${concatListPath}`);
            console.log(`Concat list content:\n${concatContent}`);

            // Use concat demuxer (more reliable than filter)
            command
                .input(concatListPath)
                .inputOptions(['-f', 'concat', '-safe', '0'])
                .outputOptions([
                    '-c', 'copy',  // Copy streams without re-encoding for speed
                    '-avoid_negative_ts', 'make_zero'
                ])
                .output(outputPath)
                .on('start', (commandLine) => {
                    console.log('FFmpeg concatenation command:', commandLine);
                    onProgress(40, 'FFmpeg processing started...');
                })
                .on('stderr', (stderrLine) => {
                    console.log(`FFmpeg concat stderr: ${stderrLine}`);
                })
                .on('progress', (progress) => {
                    const percent = Math.min(90, 40 + (progress.percent || 0) * 0.5);
                    onProgress(percent, `Processing: ${Math.round(progress.percent || 0)}%`);
                })
                .on('end', async () => {
                    try {
                        onProgress(90, 'Cleaning up temporary files...');

                        // Clean up temporary clip files and concat list
                        for (const tempClip of tempClips) {
                            try {
                                await fs.unlink(tempClip);
                            } catch (error) {
                                console.warn(`Failed to delete temp file ${tempClip}:`, error);
                            }
                        }

                        try {
                            await fs.unlink(concatListPath);
                        } catch (error) {
                            console.warn(`Failed to delete concat list ${concatListPath}:`, error);
                        }

                        onProgress(95, 'Finalizing video...');

                        const stats = await fs.stat(outputPath);

                        onProgress(100, 'Video ready!');

                        resolve({
                            filename: outputFilename,
                            path: outputPath,
                            size: stats.size,
                            duration: calculateTotalDuration(selectedClips),
                            clips: selectedClips.map(clip => `${clip.file.originalName} (${clip.startTime.toFixed(1)}s-${(clip.startTime + clip.duration).toFixed(1)}s)`)
                        });
                    } catch (error) {
                        reject(error);
                    }
                })
                .on('error', async (error, stdout, stderr) => {
                    console.error('FFmpeg concatenation error:', error);
                    console.error('FFmpeg stdout:', stdout);
                    console.error('FFmpeg stderr:', stderr);

                    // Clean up temporary clip files and concat list on error
                    for (const tempClip of tempClips) {
                        try {
                            await fs.unlink(tempClip);
                        } catch (cleanupError) {
                            console.warn(`Failed to delete temp file ${tempClip}:`, cleanupError);
                        }
                    }

                    try {
                        await fs.unlink(concatListPath);
                    } catch (cleanupError) {
                        console.warn(`Failed to delete concat list ${concatListPath}:`, cleanupError);
                    }

                    reject(error);
                })
                .run();
                
        } catch (error) {
            reject(error);
        }
    });
}

// Helper function to select random clips
function selectRandomClips(files, targetDuration) {
    const clips = [];
    let totalDuration = 0;
    const usedFiles = new Set();
    
    const availableFiles = files.filter(file => !usedFiles.has(file.id));
    
    while (totalDuration < targetDuration && availableFiles.length > 0) {
        // Pick a random file
        const randomIndex = Math.floor(Math.random() * availableFiles.length);
        const selectedFile = availableFiles[randomIndex];
        
        // Remove from available files
        availableFiles.splice(randomIndex, 1);
        usedFiles.add(selectedFile.id);
        
        // Generate random clip duration (5-30 seconds)
        const minClipDuration = 5;
        const maxClipDuration = Math.min(30, targetDuration - totalDuration + 5);
        const clipDuration = Math.random() * (maxClipDuration - minClipDuration) + minClipDuration;
        
        clips.push({
            file: selectedFile,
            duration: clipDuration,
            startTime: Math.random() * 30 // Random start time within first 30 seconds
        });
        
        totalDuration += clipDuration;
        
        if (totalDuration >= targetDuration * 0.9) {
            break;
        }
    }
    
    return clips;
}

// Helper function to calculate total duration
function calculateTotalDuration(clips) {
    return clips.reduce((total, clip) => total + clip.duration, 0);
}

// Helper function to clean old files
async function cleanOldFiles(directory, maxAge) {
    try {
        const files = await fs.readdir(directory);
        const now = Date.now();
        
        for (const file of files) {
            const filePath = path.join(directory, file);
            const stats = await fs.stat(filePath);
            
            if (now - stats.mtime.getTime() > maxAge) {
                await fs.unlink(filePath);
                console.log(`Cleaned up old file: ${file}`);
            }
        }
    } catch (error) {
        console.error('Error cleaning files:', error);
    }
}
